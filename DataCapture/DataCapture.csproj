<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <UserSecretsId>aspnet-DataCapture-b6f966cd-9e69-4cb9-831d-239b15892fc7</UserSecretsId>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.1" />
        <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="9.0.3" />
        <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.3" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.3">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.1" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.1">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.Extensions.Localization" Version="9.0.1" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Application\Application.csproj" />
      <ProjectReference Include="..\Infrastructure\Infrastructure.csproj" />
    </ItemGroup>

</Project>
