namespace Application.Abstraction.Mediator;

/// <summary>
/// Rozhraní pro požadavek, kter<PERSON> je zpracován mediátorem a vrací výsledek.
/// Slouží jako marker interface pro identifika<PERSON> tříd, kter<PERSON> představují po<PERSON>.
/// </summary>
/// <typeparam name="TResponse"><PERSON><PERSON> od<PERSON>v<PERSON>, který bude vrácen po zpracování požadavku.</typeparam>
public interface IRequest<TResponse> { }