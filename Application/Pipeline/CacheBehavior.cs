using Application.Abstraction;
using Application.Abstraction.Mediator;
using Application.Features.Generic;

namespace Application.Pipeline;

public class CacheBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : ICachableQuery<TResponse>, IRequest<TResponse>
{
    private readonly ICacheService _cacheService;

    public CacheBehavior(ICacheService cacheService)
    {
        _cacheService = cacheService;
    }

    public async Task<TResponse> Handle(
        TRequest request,
        RequestHandlerDelegate<TResponse> next,
        CancellationToken cancellationToken)
    {
        // Zkusíme zeptat cache
        var cached = await _cacheService.GetAsync<TResponse>(request.CacheKey, cancellationToken);
        if (cached is not null)
            return cached;

        // <PERSON>ak zavoláme handler a ulo<PERSON><PERSON><PERSON> výsledek
        var response = await next();
        await _cacheService.SetAsync(request.CacheKey, response, TimeSpan.FromMinutes(10), cancellationToken);
        return response;
    }
}
