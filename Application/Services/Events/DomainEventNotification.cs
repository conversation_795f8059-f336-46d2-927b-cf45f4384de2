using Application.Abstraction.Mediator;
using Domain;
using SharedKernel.Abstractions;

namespace Application.Services.Events;

public class DomainEventNotification<TDomainEvent> : INotification
    where TDomainEvent : DomainEvent  // <PERSON><PERSON>ová událost z Domain Layer
{
    public TDomainEvent DomainEvent { get; }

    public DomainEventNotification(TDomainEvent domainEvent)
    {
        DomainEvent = domainEvent;
    }
}