using Application.Abstraction;
using Application.Abstraction.Mediator;
using Application.Services.Mapper;
using SharedKernel.Abstractions;
using SharedKernel.Models;

namespace Application.Features.Generic.Commands;

// 1.1) Command
public class CreateEntityCommand<TEntity, TEditDto, TKey> : IRequest<Result<TKey>>, IInvalidateCache
    where TEditDto : class
{
    public TEditDto Payload { get; init; } = default!;
    // Vymaže všechno pro tuhle entitu
    public IEnumerable<string>? CacheTags 
        => new[] { typeof(TEntity).Name };

    public IEnumerable<string> CacheKeys { get; }
}


// 1.2) Handler
public class CreateEntityCommandHandler<TEntity, TEditDto, TKey>
    : IRequestHandler<CreateEntityCommand<TEntity,TEditDto, TKey>, Result<TKey>>
    where TEntity : BaseEntity<TKey>, new()
    where TEditDto : class
{
    private readonly IApplicationDbContext _ctx;
    private readonly IMapper<TEditDto, TEntity> _mapper;

    public CreateEntityCommandHandler(
        IApplicationDbContext ctx,
        IMapper<TEditDto, TEntity> mapper)
    {
        _ctx = ctx;
        _mapper = mapper;
    }

    public async Task<Result<TKey>> Handle(
        CreateEntityCommand<TEntity, TEditDto, TKey> request, 
        CancellationToken ct)
    {
        // 1) Map payload to entity
        var entity = _mapper.MapDirect(request.Payload);

        // 2) Add + Save
        await _ctx.Set<TEntity>().AddAsync(entity, ct);
        await _ctx.SaveChangesAsync(ct);

        // 3) Vrať nové id
        return await Result<TKey>.OkAsync(entity.Id);
    }
}
