
using Application.Abstraction.Mediator;

namespace Application.Features.Generic;

public interface IQuery<TResult> : IRequest<TResult>;

public interface ICachableQuery<TResult>
{
    /// <summary>
    /// Jedinečný klíč pro cache (např. "GetUserById:1234").
    /// </summary>
    string <PERSON><PERSON><PERSON><PERSON> { get; }
}

public interface IInvalidateCache
{
    /// <summary>
    /// Jeden nebo více kl<PERSON>, které se mají po provedení příkazu vymazat.
    /// </summary>
    IEnumerable<string> CacheKeys { get; }
}