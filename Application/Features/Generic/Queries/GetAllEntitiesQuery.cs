using Application.Abstraction;
using Application.Abstraction.Mediator;
using Application.Exceptions;
using Application.Models;
using Application.Services;
using Application.Services.Mapper;
using Microsoft.EntityFrameworkCore;
using SharedKernel.Models;

namespace Application.Features.Generic.Queries;

public class GetAllEntitiesQuery<TDto> : IRequest<Result<List<TDto>>>;

public class GetAllEntitiesQueryHandler<TEntity, TDto>
    : GenericCollectionQueryHandler<TEntity, TDto, GetAllEntitiesQuery<TDto>>
    where TEntity : class
    where TDto : class, new()
{
    public GetAllEntitiesQueryHandler(IApplicationDbContext context, IMapper<TEntity, TDto> mapper)
        : base(context, mapper)
    {
    }

    protected override async Task<List<TDto>> FetchCollectionAsync(GetAllEntitiesQuery<TDto> request,
        CancellationToken cancellationToken)
    {
        var data = await Context.Set<TEntity>().ToListAsync(cancellationToken);
        if (data is null)
            throw new NotFoundException(typeof(TEntity).Name, typeof(TDto).Name);

        // Vracíme přímo kole<PERSON>to
        return Mapper.MapDirectCollection(data).ToList();
    }
}