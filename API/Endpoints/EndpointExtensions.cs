namespace API.Endpoints;

/// <summary>
/// Rozšiřující metody pro registraci všech endpointů aplikace.
/// </summary>
public static class EndpointExtensions
{
    /// <summary>
    /// Registruje všechny endpointy aplikace.
    /// </summary>
    /// <param name="app">WebApplication instance</param>
    /// <returns>WebApplication pro fluent API</returns>
    public static WebApplication MapAllEndpoints(this WebApplication app)
    {
        // Zde můžete přidat dalš<PERSON> skupiny endpointů:
        app.MapSampleEntityEndpoints();
        // app.MapUserEndpoints();
        // atd.

        return app;
    }
}
