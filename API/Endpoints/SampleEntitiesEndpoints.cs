using Application.Abstraction.Mediator;
using Application.Features.Generic.Commands;
using Application.Features.Generic.Queries;
using Application.Features.Sample;
using Domain.Entities;
using Microsoft.AspNetCore.Mvc;

namespace API.Endpoints;

public static class SampleEntitiesEndpoints
{
    public static IEndpointRouteBuilder MapSampleEntityEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/v1/sample-entities")
            .WithTags("SampleEntities")
            .RequireAuthorization();

        group.MapGet("/", async ([FromServices] IMediator mediator) =>
        {
            //var items = await mediator.Send(new GetAllSamplesQuery());
            var items = await mediator.Send(new GetAllEntitiesQuery<SampleDto>());
            if (!items.Succeeded)
                return Results.BadRequest(items.Errors);
            return Results.Ok(items);
        });
        
        // GET /my-entities/{id} - Získání entity podle ID
        group.MapGet("/{id:int}", async (int id, [FromServices] IMediator mediator) =>
        {
            var entity = await mediator.Send(new GetEntityByIdQuery<SampleDto> { Id = id });
            return entity is not null ? Results.Ok(entity) : Results.NotFound();
        })

        group.MapPost("/", async ([FromBody] SampleAddEdit input, [FromServices] IMediator mediator) =>
        {
            var result = await mediator.Send(new CreateEntityCommand<SampleEntity, SampleAddEdit, int> { Payload = input });
            if (!result.Succeeded)
                return Results.BadRequest(result.Errors);
            return Results.Created($"/v1/sample-entities/{result.Data}", result.Data);
        });
        
        group.MapPut("/{id:int}", async (int id, SampleAddEdit input, IMediator mediator) =>
        {
            var result = await mediator.Send(new UpdateEntityCommand<SampleEntity, SampleAddEdit, int> { Id = id, Payload = input });
            if (!result.Succeeded)
                return Results.BadRequest(result.Errors);
            return result ? Results.NoContent() : Results.NotFound();
            
            
            var ok = await service.UpdateAsync(id, input);
            return ok ? Results.NoContent() : Results.NotFound();
        });

        group.MapDelete("/{id:int}", async (int id, IMediator mediator) =>
        {
            var ok = await service.DeleteAsync(id);
            return ok ? Results.NoContent() : Results.NotFound();
        });

        return app;
    }
}