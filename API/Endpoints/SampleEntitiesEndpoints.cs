using Application.Abstraction.Mediator;
using Application.Features.Generic.Commands;
using Application.Features.Generic.Queries;
using Application.Features.Sample;
using Domain.Entities;
using Microsoft.AspNetCore.Mvc;

namespace API.Endpoints;

public static class SampleEntitiesEndpoints
{
    public static IEndpointRouteBuilder MapSampleEntityEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/v1/sample-entities")
            .WithTags("SampleEntities")
            .RequireAuthorization();

        group.MapGet("/", async ([FromServices] IMediator mediator) =>
        {
            var items = await mediator.Send(new GetAllEntitiesQuery<SampleDto>());
            if (!items.Succeeded)
                return Results.BadRequest(items.Errors);
            return Results.Ok(items);
        });
        
        group.MapGet("/{id:int}", async (int id, [FromServices] IMediator mediator) =>
        {
            var entity = await mediator.Send(new GetEntityByIdQuery<SampleDto> { Id = id });
            return entity is not null ? Results.Ok(entity) : Results.NotFound();
        });

        group.MapPost("/", async ([FromBody] SampleAddEdit input, [FromServices] IMediator mediator) =>
        {
            var result = await mediator.Send(new CreateEntityCommand<SampleEntity, SampleAddEdit, int> { Payload = input });
            if (!result.Succeeded)
                return Results.BadRequest(result.Errors);
            return Results.Created($"/v1/sample-entities/{result.Data}", result.Data);
        });
        
        group.MapPut("/{id:int}", async (int id, SampleAddEdit input, IMediator mediator) =>
        {
            var result = await mediator.Send(new UpdateEntityCommand<SampleEntity, SampleAddEdit, int> { Id = id, Payload = input });
            if (!result.Succeeded)
                return Results.BadRequest(result.Errors);
            return result.Data ? Results.Ok(result.Data) : Results.NotFound();
            
        });

        group.MapDelete("/{id:int}", async (int id, IMediator mediator) =>
        {
            var result = await mediator.Send(new DeleteEntityCommand<SampleEntity, int> { Id = id});
            if (!result.Succeeded)
                return Results.BadRequest(result.Errors);
            return result.Data ? Results.Ok(result.Data) : Results.NotFound();
        });

        return app;
    }
}