using Application.Abstraction.Mediator;
using Application.Features.Generic.Commands;
using Application.Features.Generic.Queries;
using Application.Features.Sample;
using Application.Features.Sample.Queries;
using Domain.Entities;

namespace API.Endpoints;

public static class SampleEntitiesEndpoints
{
    public static IEndpointRouteBuilder MapSampleEntityEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/v1/sample-entities")
            .WithTags("SampleEntities")
            .RequireAuthorization();

        group.MapGet("/", async (IMediator mediator) =>
        {
            //var items = await mediator.Send(new GetAllSamplesQuery());
            var items = await mediator.Send(new GetAllEntitiesQuery<SampleDto>());
            if (!items.Succeeded)
                return Results.BadRequest(items.Errors);
            return Results.Ok(items);
        });
        
        // GET /my-entities/{id} - Získání entity podle ID
        group.MapGet("/{id:int}", async (int id, IMediator mediator) => 
            {
                var entity = await mediator.Send(new GetEntityByIdQuery<SampleDto> { Id = id });
                return entity is not null ? Results.Ok(entity) : Results.NotFound();
            })
            .WithName("GetEntityById")
            .Produces<SampleDto>(StatusCodes.Status200OK)
            .Produces(StatusCodes.Status404NotFound);

        group.MapPost("/", async (SampleAddEdit input, IMediator mediator) =>
        {
            var id = await mediator.Send(new CreateEntityCommand<SampleEntity, SampleAddEdit, int>());
            return Results.Created($"/v1/sample-entities/{id}", id);
        });

        // a tak dál...

        return app;
    }
}