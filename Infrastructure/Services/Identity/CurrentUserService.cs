using System.Security.Claims;
using Application.Abstraction;
using Application.Services;
using Domain.Identity;
using Infrastructure.Persistence;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Services;

public class CurrentUserService : ICurrentUserService
{
    private readonly IHttpContextAccessor _httpContext;
    private readonly ApplicationDbContext _applicationDbContext;
    private UserProfile? _profile;

    public CurrentUserService(
        IHttpContextAccessor httpContext,
        ApplicationDbContext applicationDbContext)
    {
        _httpContext = httpContext;
        _applicationDbContext = applicationDbContext;
    }

    private ClaimsPrincipal? User => _httpContext.HttpContext?.User;

    public string? UserId =>
        User?.FindFirstValue(ClaimTypes.NameIdentifier);

    public string? Email =>
        User?.FindFirstValue(ClaimTypes.Email);

    public IEnumerable<string> Roles =>
        User?
            .FindAll(ClaimTypes.Role)
            .Select(c => c.Value)
        ?? Enumerable.Empty<string>();

    public UserProfile? Profile
    {
        get
        {
            if (_profile == null && int.TryParse(UserId, out var uid))
            {
                _profile = _applicationDbContext.UserProfiles
                    .AsNoTracking()
                    .FirstOrDefault(up => up.UserId == uid);
            }
            return _profile;
        }
    }
}